import { createWorker } from 'tesseract.js';
import { OCRResult, OCRConfig, MedicineInfo, MedicineIdentificationResult } from '@/types/ocr';

/**
 * Medical OCR class for extracting text from medicine images using Tesseract.js
 */
export class MedicalOCR {
  private worker: any | null = null;
  private isInitialized = false;
  private config: OCRConfig;

  constructor(config: OCRConfig = {}) {
    this.config = {
      language: 'eng',
      minConfidence: 40, // Lowered from 60 to be more permissive
      debug: true, // Enable debug by default for better troubleshooting
      tesseractOptions: {
        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 .,-()/',
        tessedit_pageseg_mode: '6', // PSM.SINGLE_BLOCK - good for medicine boxes
        tessedit_ocr_engine_mode: '1', // Use LSTM OCR engine mode for better accuracy
      },
      ...config,
    };
  }

  /**
   * Initialize the Tesseract worker
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('🔄 Initializing Tesseract OCR worker...');

      // Create worker using Tesseract.js v6 API - createWorker returns a Promise and is already initialized
      this.worker = await createWorker(this.config.language || 'eng');

      console.log('🔄 Worker created and initialized successfully');

      // Set custom parameters if provided
      if (this.config.tesseractOptions) {
        console.log('🔄 Setting custom parameters...');
        await this.worker.setParameters(this.config.tesseractOptions);
      }

      this.isInitialized = true;
      console.log('✅ Tesseract OCR worker ready for use');
    } catch (error) {
      console.error('❌ Failed to initialize Tesseract worker:', error);
      this.worker = null;
      this.isInitialized = false;
      throw new Error(`OCR initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Preprocess image to improve OCR accuracy
   */
  private async preprocessImage(imageFile: File): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();

      img.onload = () => {
        // Set canvas size
        canvas.width = img.width;
        canvas.height = img.height;

        // Draw original image
        ctx.drawImage(img, 0, 0);

        // Get image data for processing
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Apply image enhancements
        for (let i = 0; i < data.length; i += 4) {
          // Convert to grayscale first
          const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];

          // Increase contrast and brightness
          const contrast = 1.5; // Increase contrast
          const brightness = 20; // Increase brightness

          let enhanced = contrast * (gray - 128) + 128 + brightness;
          enhanced = Math.max(0, Math.min(255, enhanced));

          // Apply threshold for better text clarity
          const threshold = 128;
          const final = enhanced > threshold ? 255 : 0;

          data[i] = final;     // Red
          data[i + 1] = final; // Green
          data[i + 2] = final; // Blue
          // Alpha channel stays the same
        }

        // Put processed image data back
        ctx.putImageData(imageData, 0, 0);

        // Convert canvas to blob and then to File
        canvas.toBlob((blob) => {
          if (blob) {
            const processedFile = new File([blob], `processed_${imageFile.name}`, {
              type: 'image/png'
            });
            console.log('🖼️ Image preprocessing completed');
            resolve(processedFile);
          } else {
            console.log('⚠️ Image preprocessing failed, using original');
            resolve(imageFile);
          }
        }, 'image/png');
      };

      img.onerror = () => {
        console.log('⚠️ Image preprocessing failed, using original');
        resolve(imageFile);
      };

      img.src = URL.createObjectURL(imageFile);
    });
  }

  /**
   * Extract text from an image file with preprocessing
   */
  async extractText(imageFile: File): Promise<OCRResult> {
    if (!this.isInitialized || !this.worker) {
      await this.initialize();
    }

    const startTime = Date.now();

    try {
      console.log(`🔄 Starting OCR processing for: ${imageFile.name}`);

      // Preprocess image for better OCR accuracy
      console.log('🖼️ Preprocessing image for better OCR...');
      const processedImage = await this.preprocessImage(imageFile);

      // Try OCR with multiple configurations for better results
      const ocrAttempts = [
        // Attempt 1: Standard configuration
        {
          options: {
            ...this.config.tesseractOptions,
            tessedit_pageseg_mode: '6', // Single block
          },
          description: 'Standard OCR'
        },
        // Attempt 2: Single line mode for medicine names
        {
          options: {
            ...this.config.tesseractOptions,
            tessedit_pageseg_mode: '8', // Single word
          },
          description: 'Single word OCR'
        },
        // Attempt 3: Auto page segmentation
        {
          options: {
            ...this.config.tesseractOptions,
            tessedit_pageseg_mode: '3', // Auto page segmentation
          },
          description: 'Auto segmentation OCR'
        }
      ];

      let bestResult: OCRResult | null = null;

      for (const attempt of ocrAttempts) {
        try {
          console.log(`🔄 Trying ${attempt.description}...`);

          // Update worker options
          await this.worker!.setParameters(attempt.options);

          const { data } = await this.worker!.recognize(processedImage);
          const processingTime = Date.now() - startTime;

          const result: OCRResult = {
            text: data.text.trim(),
            confidence: data.confidence,
            processingTime,
            words: data.words || [],
            success: true,
          };

          console.log(`📊 ${attempt.description} result: "${result.text}" (${data.confidence.toFixed(1)}% confidence)`);

          // Keep the best result (highest confidence with meaningful text)
          if (!bestResult || (data.confidence > bestResult.confidence && result.text.length > 3)) {
            bestResult = result;
          }

          // If we get good confidence, use it
          if (data.confidence > 60 && result.text.length > 5) {
            break;
          }

        } catch (attemptError) {
          console.log(`⚠️ ${attempt.description} failed:`, attemptError);
          continue;
        }
      }

      if (bestResult) {
        console.log(`✅ Best OCR result: "${bestResult.text}" (${bestResult.confidence.toFixed(1)}% confidence)`);

        if (this.config.debug) {
          console.log('📝 Extracted text:', bestResult.text);
          console.log('📊 Word details:', bestResult.words);
        }

        return bestResult;
      } else {
        throw new Error('All OCR attempts failed');
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown OCR error';
      
      console.error('❌ OCR processing failed:', errorMessage);

      return {
        text: '',
        confidence: 0,
        processingTime,
        words: [],
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Extract medicine-specific information from OCR text
   */
  extractMedicineInfo(ocrText: string): MedicineInfo {
    const cleanText = this.cleanText(ocrText);
    const potentialNames = this.extractPotentialMedicineNames(cleanText);
    const dosageInfo = this.extractDosageInfo(cleanText);
    const medicineType = this.identifyMedicineType(cleanText);

    return {
      potentialNames,
      dosageInfo,
      medicineType,
      confidence: this.calculateMedicineConfidence(potentialNames, dosageInfo),
      rawText: ocrText,
      cleanedText: cleanText,
    };
  }

  /**
   * Complete medicine identification process
   */
  async identifyMedicine(imageFile: File): Promise<MedicineIdentificationResult> {
    try {
      console.log('🚀 Starting complete medicine identification process...');
      
      // Step 1: Extract text using OCR
      const ocrResult = await this.extractText(imageFile);
      
      if (!ocrResult.success) {
        return {
          ocrResult,
          medicineInfo: this.getEmptyMedicineInfo(),
          success: false,
        };
      }

      // Step 2: Extract medicine-specific information
      const medicineInfo = this.extractMedicineInfo(ocrResult.text);
      
      // Step 3: Identify the most likely medicine
      const identifiedMedicine = this.selectBestMedicine(medicineInfo.potentialNames);

      // More intelligent success criteria
      const hasValidMedicine = identifiedMedicine && identifiedMedicine.length >= 3;
      const hasReasonableConfidence = medicineInfo.confidence >= (this.config.minConfidence || 40);
      const ocrWorked = ocrResult.success && ocrResult.text.length > 0;

      const result: MedicineIdentificationResult = {
        ocrResult,
        medicineInfo,
        identifiedMedicine,
        success: ocrWorked && (hasValidMedicine || hasReasonableConfidence),
      };

      console.log(`📊 Success evaluation:`);
      console.log(`  - OCR worked: ${ocrWorked}`);
      console.log(`  - Valid medicine found: ${hasValidMedicine} (${identifiedMedicine})`);
      console.log(`  - Confidence: ${medicineInfo.confidence}% (min: ${this.config.minConfidence})`);
      console.log(`  - Overall success: ${result.success}`);

      console.log(`✅ Medicine identification completed. Success: ${result.success}`);
      if (result.identifiedMedicine) {
        console.log(`🎯 Identified medicine: ${result.identifiedMedicine}`);
      }

      return result;
    } catch (error) {
      console.error('❌ Medicine identification failed:', error);
      
      return {
        ocrResult: {
          text: '',
          confidence: 0,
          processingTime: 0,
          words: [],
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        medicineInfo: this.getEmptyMedicineInfo(),
        success: false,
      };
    }
  }

  /**
   * Clean and normalize text for better processing
   */
  private cleanText(text: string): string {
    return text
      .replace(/[^\w\s\d.-]/g, ' ') // Remove special characters except dots and hyphens
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .toLowerCase();
  }

  /**
   * Smart extraction of potential medicine names using multiple strategies
   */
  private extractPotentialMedicineNames(cleanText: string): string[] {
    console.log(`🔍 Extracting medicine names from: "${cleanText}"`);

    const potentialNames: string[] = [];
    const words = cleanText.split(/\s+/).filter(word => word.length > 0);

    // Strategy 1: Individual word analysis
    console.log('📝 Strategy 1: Analyzing individual words...');
    for (let i = 0; i < words.length; i++) {
      const word = words[i];

      // Skip very short words or pure numbers
      if (word.length < 3 || /^\d+$/.test(word)) continue;

      // Check if word matches medicine patterns
      if (this.isMedicineNamePattern(word)) {
        potentialNames.push(word);
        console.log(`✅ Individual word match: "${word}"`);

        // Check for compound names (e.g., "para cetamol", "ibu profen")
        if (i + 1 < words.length) {
          const nextWord = words[i + 1];
          if (this.isMedicineModifier(nextWord) || this.isMedicineNamePattern(nextWord)) {
            const compoundName = `${word}${nextWord}`; // Join without space for compound medicines
            potentialNames.push(compoundName);
            console.log(`✅ Compound medicine: "${compoundName}"`);
          }
        }
      }
    }

    // Strategy 2: Look for medicine names that might be split or have OCR errors
    console.log('📝 Strategy 2: Checking for split/corrupted medicine names...');
    const joinedText = cleanText.replace(/\s+/g, '').toLowerCase();
    const commonMedicines = [
      'paracetamol', 'acetaminophen', 'ibuprofen', 'aspirin', 'diclofenac',
      'amoxicillin', 'azithromycin', 'ciprofloxacin', 'metformin', 'atorvastatin',
      'omeprazole', 'pantoprazole', 'cetirizine', 'loratadine', 'prednisolone',
      'amlodipine', 'lisinopril', 'simvastatin', 'warfarin', 'furosemide'
    ];

    for (const medicine of commonMedicines) {
      // Check for partial matches (allowing for OCR errors)
      if (this.fuzzyMatch(joinedText, medicine, 0.7)) {
        potentialNames.push(medicine);
        console.log(`✅ Fuzzy match found: "${medicine}" in "${joinedText}"`);
      }
    }

    // Strategy 3: Extract from original text (before cleaning) to catch capitalization
    console.log('📝 Strategy 3: Analyzing original capitalization...');
    const originalWords = cleanText.split(/\s+/);
    originalWords.forEach(word => {
      // Look for properly capitalized brand names
      if (/^[A-Z][a-z]+/.test(word) && word.length >= 4 && word.length <= 15) {
        if (this.isMedicineNamePattern(word.toLowerCase()) && !potentialNames.includes(word.toLowerCase())) {
          potentialNames.push(word.toLowerCase());
          console.log(`✅ Capitalized brand name: "${word}"`);
        }
      }
    });

    // Remove duplicates and sort by length (longer names often more specific)
    const uniqueNames = [...new Set(potentialNames)].sort((a, b) => b.length - a.length);
    console.log(`📋 Final ${uniqueNames.length} potential medicine names:`, uniqueNames);
    return uniqueNames;
  }

  /**
   * Enhanced fuzzy string matching for OCR error tolerance
   */
  private fuzzyMatch(text: string, target: string, threshold: number = 0.8): boolean {
    const textLen = text.length;
    const targetLen = target.length;

    // Simple substring check first
    if (text.includes(target) || target.includes(text)) return true;

    // Check for common OCR character substitutions
    const ocrCorrectedText = this.correctCommonOCRErrors(text);
    if (ocrCorrectedText.includes(target) || target.includes(ocrCorrectedText)) return true;

    // Check for partial matches with common prefixes/suffixes
    const targetParts = this.getImportantParts(target);
    for (const part of targetParts) {
      if (part.length >= 4 && text.includes(part)) {
        console.log(`🎯 Found important part "${part}" of "${target}" in text`);
        return true;
      }
    }

    // Levenshtein distance-based similarity with adjusted threshold for shorter words
    const maxLen = Math.max(textLen, targetLen);
    if (maxLen === 0) return true;

    // Use more lenient threshold for shorter medicine names
    const adjustedThreshold = targetLen <= 6 ? Math.max(0.6, threshold - 0.2) : threshold;

    const distance = this.levenshteinDistance(text, target);
    const similarity = (maxLen - distance) / maxLen;

    return similarity >= adjustedThreshold;
  }

  /**
   * Correct common OCR character substitutions
   */
  private correctCommonOCRErrors(text: string): string {
    return text
      .replace(/[0O]/g, 'o')  // 0 and O often confused
      .replace(/[1Il|]/g, 'i') // 1, I, l, | often confused
      .replace(/[5S]/g, 's')   // 5 and S often confused
      .replace(/[6G]/g, 'g')   // 6 and G often confused
      .replace(/[8B]/g, 'b')   // 8 and B often confused
      .replace(/rn/g, 'm')     // rn often read as m
      .replace(/vv/g, 'w')     // vv often read as w
      .replace(/cl/g, 'd')     // cl often read as d
      .replace(/[\/\\]/g, 'l') // slashes often read as l
      .replace(/[.,]/g, '')    // Remove punctuation
      .replace(/\s+/g, '');    // Remove spaces
  }

  /**
   * Extract important parts of medicine names for partial matching
   */
  private getImportantParts(medicineName: string): string[] {
    const parts: string[] = [];

    // Add the full name
    parts.push(medicineName);

    // Add significant prefixes (first 4+ characters)
    if (medicineName.length >= 6) {
      parts.push(medicineName.substring(0, 4));
      parts.push(medicineName.substring(0, 5));
    }

    // Add significant suffixes for compound names
    if (medicineName.length >= 8) {
      parts.push(medicineName.substring(medicineName.length - 4));
      parts.push(medicineName.substring(medicineName.length - 5));
    }

    // Add root parts for common medicine patterns
    const commonRoots = ['para', 'acet', 'ibu', 'aspir', 'ferv', 'doli'];
    for (const root of commonRoots) {
      if (medicineName.toLowerCase().includes(root)) {
        parts.push(root);
      }
    }

    return parts.filter(part => part.length >= 3);
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Smart medicine name detection - automatically identifies medicine names without hardcoded lists
   */
  private isMedicineNamePattern(word: string): boolean {
    const lowerWord = word.toLowerCase();

    // Skip if too short or contains only numbers
    if (word.length < 3 || /^\d+$/.test(word)) return false;

    // Skip common non-medicine words
    const excludeWords = [
      'tablets', 'capsules', 'syrup', 'injection', 'cream', 'ointment', 'gel', 'drops',
      'medicine', 'pharmaceutical', 'company', 'limited', 'pvt', 'ltd', 'inc',
      'manufactured', 'by', 'for', 'use', 'only', 'prescription', 'required',
      'store', 'room', 'temperature', 'keep', 'away', 'children', 'expiry',
      'batch', 'mfg', 'exp', 'date', 'lot', 'no', 'pack', 'size', 'strip',
      'blister', 'bottle', 'vial', 'ampoule', 'sachet', 'tube', 'box',
      'warning', 'caution', 'side', 'effects', 'dosage', 'instructions'
    ];

    if (excludeWords.includes(lowerWord)) return false;

    // SMART DETECTION: Look for medicine-like characteristics
    const medicineIndicators = {
      // Common medicine name endings (expanded list)
      endings: ['ol', 'in', 'an', 'ex', 'ine', 'ate', 'ide', 'yl', 'al', 'il', 'on', 'um', 'ic', 'ase', 'ene', 'one'],

      // Medicine name prefixes
      prefixes: ['para', 'anti', 'pro', 'pre', 'meta', 'hydro', 'chlor', 'sulfa', 'amino', 'acetyl'],

      // Medicine name patterns (contains these substrings)
      patterns: ['cetam', 'phen', 'cycl', 'mycin', 'cillin', 'zole', 'prazole', 'sartan', 'statin', 'pine', 'zine'],

      // Chemical/pharmaceutical suffixes
      chemicalSuffixes: ['hydrochloride', 'sulfate', 'phosphate', 'acetate', 'citrate', 'tartrate', 'maleate', 'fumarate']
    };

    // Check for medicine-like patterns
    const hasEnding = medicineIndicators.endings.some(ending => lowerWord.endsWith(ending));
    const hasPrefix = medicineIndicators.prefixes.some(prefix => lowerWord.startsWith(prefix));
    const hasPattern = medicineIndicators.patterns.some(pattern => lowerWord.includes(pattern));
    const hasChemicalSuffix = medicineIndicators.chemicalSuffixes.some(suffix => lowerWord.includes(suffix));

    // Additional smart checks
    const hasCapitalLetters = /[A-Z]/.test(word) && word !== word.toUpperCase(); // Mixed case suggests brand name
    const hasNumbers = /\d/.test(word); // Medicine names often have numbers
    const isReasonableLength = word.length >= 4 && word.length <= 20; // Reasonable medicine name length

    // Score-based detection
    let score = 0;
    if (hasEnding) score += 3;
    if (hasPrefix) score += 2;
    if (hasPattern) score += 3;
    if (hasChemicalSuffix) score += 4;
    if (hasCapitalLetters) score += 1;
    if (hasNumbers) score += 1;
    if (isReasonableLength) score += 1;

    // Consider it a medicine if score is high enough
    const isMedicine = score >= 3;

    if (isMedicine) {
      console.log(`🎯 Smart detection: "${word}" scored ${score} points - identified as medicine`);
    }

    return isMedicine;
  }

  /**
   * Check if a word is a medicine modifier (strength, type, etc.)
   */
  private isMedicineModifier(word: string): boolean {
    const modifiers = ['extra', 'strength', 'forte', 'plus', 'max', 'rapid', 'extended'];
    return modifiers.includes(word);
  }

  /**
   * Extract dosage information from text
   */
  private extractDosageInfo(cleanText: string): string | undefined {
    const dosagePatterns = [
      /(\d+)\s*(mg|g|ml|mcg|iu)/gi,
      /(\d+)\s*(milligram|gram|milliliter|microgram)/gi,
    ];

    for (const pattern of dosagePatterns) {
      const match = cleanText.match(pattern);
      if (match) {
        return match[0];
      }
    }

    return undefined;
  }

  /**
   * Identify medicine type/category
   */
  private identifyMedicineType(cleanText: string): string | undefined {
    const typeKeywords = {
      'pain relief': ['pain', 'relief', 'analgesic', 'aspirin', 'ibuprofen', 'tylenol'],
      'antibiotic': ['antibiotic', 'amoxicillin', 'penicillin', 'azithromycin'],
      'vitamin': ['vitamin', 'supplement', 'multivitamin'],
      'allergy': ['allergy', 'antihistamine', 'benadryl', 'claritin'],
    };

    for (const [type, keywords] of Object.entries(typeKeywords)) {
      if (keywords.some(keyword => cleanText.includes(keyword))) {
        return type;
      }
    }

    return undefined;
  }

  /**
   * Smart confidence calculation for medicine identification
   */
  private calculateMedicineConfidence(potentialNames: string[], dosageInfo?: string): number {
    let confidence = 0;

    console.log(`🎯 Calculating confidence for ${potentialNames.length} potential names:`, potentialNames);

    // Base confidence from having potential names (more generous)
    if (potentialNames.length > 0) {
      confidence += 60; // Increased from 40
      console.log(`✅ Base confidence for having names: +60`);
    }

    // Bonus for multiple potential names
    const multipleNamesBonus = Math.min(potentialNames.length * 5, 20);
    confidence += multipleNamesBonus;
    console.log(`✅ Multiple names bonus: +${multipleNamesBonus}`);

    // Bonus for having dosage information
    if (dosageInfo) {
      confidence += 15;
      console.log(`✅ Dosage info bonus: +15`);
    }

    // Expanded list of recognizable medicine names (including international brands!)
    const knownMedicines = [
      'paracetamol', 'acetaminophen', 'aspirin', 'ibuprofen', 'diclofenac',
      'tylenol', 'advil', 'panadol', 'amoxicillin', 'azithromycin',
      'omeprazole', 'pantoprazole', 'metformin', 'atorvastatin',
      'cetirizine', 'loratadine', 'prednisolone', 'amlodipine',
      // French medicines
      'fervex', 'doliprane', 'efferalgan', 'dafalgan', 'pheniramine',
      // More international brands
      'nurofen', 'voltaren', 'brufen', 'calpol', 'lemsip'
    ];

    const recognizableNames = potentialNames.filter(name =>
      knownMedicines.some(known =>
        name.toLowerCase().includes(known) || known.includes(name.toLowerCase())
      )
    );

    if (recognizableNames.length > 0) {
      const recognitionBonus = recognizableNames.length * 10;
      confidence += recognitionBonus;
      console.log(`✅ Recognized medicines (${recognizableNames.join(', ')}): +${recognitionBonus}`);
    }

    // Bonus for medicine-like word structure
    const structuralNames = potentialNames.filter(name => {
      const lowerName = name.toLowerCase();
      return (
        lowerName.endsWith('ol') || lowerName.endsWith('in') ||
        lowerName.endsWith('ine') || lowerName.endsWith('an') ||
        lowerName.includes('para') || lowerName.includes('cetam')
      );
    });

    if (structuralNames.length > 0) {
      confidence += 10;
      console.log(`✅ Medicine-like structure bonus: +10`);
    }

    const finalConfidence = Math.min(confidence, 100);
    console.log(`📊 Final medicine confidence: ${finalConfidence}%`);

    return finalConfidence;
  }

  /**
   * Smart medicine selection using advanced scoring algorithm
   */
  private selectBestMedicine(potentialNames: string[]): string | undefined {
    if (potentialNames.length === 0) {
      console.log('❌ No potential medicine names found');
      return undefined;
    }

    console.log(`🔍 Evaluating ${potentialNames.length} potential medicine names:`, potentialNames);

    // Score each potential medicine name
    const scoredNames = potentialNames.map(name => {
      const lowerName = name.toLowerCase();
      let score = 0;

      console.log(`\n📊 Scoring "${name}":`);

      // Priority 1: Known medicine patterns (highest weight)
      const knownPatterns = [
        'paracetamol', 'acetaminophen', 'ibuprofen', 'aspirin', 'diclofenac',
        'amoxicillin', 'azithromycin', 'ciprofloxacin', 'metformin', 'atorvastatin',
        'omeprazole', 'pantoprazole', 'cetirizine', 'loratadine', 'prednisolone'
      ];

      const hasKnownPattern = knownPatterns.some(pattern =>
        lowerName.includes(pattern) || pattern.includes(lowerName)
      );
      if (hasKnownPattern) {
        score += 50;
        console.log(`✅ Known medicine pattern detected: +50 points`);
      }

      // Priority 2: Medicine-like structure
      const medicineEndings = ['ol', 'in', 'an', 'ex', 'ine', 'ate', 'ide', 'yl', 'al', 'il', 'on', 'um'];
      if (medicineEndings.some(ending => lowerName.endsWith(ending))) {
        score += 25;
        console.log(`🔤 Medicine ending detected: +25 points`);
      }

      // Priority 3: Length preference (moderate - not too short, not too long)
      const idealLength = 8; // Sweet spot for medicine names
      const lengthScore = Math.max(0, 20 - Math.abs(name.length - idealLength));
      score += lengthScore;
      console.log(`📏 Length score: +${lengthScore} points (length: ${name.length})`);

      // Priority 4: Capitalization pattern (brand names often have specific capitalization)
      if (/^[A-Z][a-z]+/.test(name)) {
        score += 15;
        console.log(`🔠 Proper capitalization: +15 points`);
      }

      // Priority 5: Contains numbers (many medicines have numbers)
      if (/\d/.test(name)) {
        score += 10;
        console.log(`🔢 Contains numbers: +10 points`);
      }

      // Priority 6: Avoid obviously wrong words
      const obviouslyWrong = ['tablets', 'capsules', 'mg', 'ml', 'strip', 'pack'];
      if (obviouslyWrong.some(wrong => lowerName.includes(wrong))) {
        score -= 30;
        console.log(`❌ Contains non-medicine word: -30 points`);
      }

      // Priority 7: Penalize very short or very long names
      if (name.length < 4) {
        score -= 20;
        console.log(`⚠️ Too short: -20 points`);
      }
      if (name.length > 20) {
        score -= 15;
        console.log(`⚠️ Too long: -15 points`);
      }

      console.log(`📊 Final score for "${name}": ${score} points`);
      return { name, score };
    });

    // Sort by score (highest first) and return the best match
    const bestMatch = scoredNames.sort((a, b) => b.score - a.score)[0];

    if (bestMatch.score > 0) {
      console.log(`🎯 Selected best medicine: "${bestMatch.name}" with ${bestMatch.score} points`);
      return bestMatch.name;
    } else {
      console.log(`❌ No medicine scored above 0 points`);
      return undefined;
    }
  }

  /**
   * Get empty medicine info structure
   */
  private getEmptyMedicineInfo(): MedicineInfo {
    return {
      potentialNames: [],
      confidence: 0,
      rawText: '',
      cleanedText: '',
    };
  }

  /**
   * Cleanup resources
   */
  async terminate(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
      console.log('🔄 Tesseract worker terminated');
    }
  }
}
